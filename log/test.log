10:30:49.076 [info] Drops.Relation.SchemaCache started with persistent storage at /Users/<USER>/Workspace/drops-relation/tmp/cache/test/drops_schema/drops_schema_cache.dets
10:30:49.663 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:30:49.663 [info] create table users
10:30:49.664 [info] == Migrated 20240101000001 in 0.0s
10:30:49.672 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:30:49.672 [info] create table groups
10:30:49.673 [info] == Migrated 20240101000002 in 0.0s
10:30:49.673 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:30:49.673 [info] create table user_groups
10:30:49.673 [info] create index user_groups_user_id_group_id_index
10:30:49.673 [info] == Migrated 20240101000003 in 0.0s
10:30:49.673 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:30:49.673 [info] create table association_parents
10:30:49.673 [info] create table associations
10:30:49.674 [info] create table association_items
10:30:49.674 [info] == Migrated 20240101000004 in 0.0s
10:30:49.674 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:30:49.674 [info] create table basic_types
10:30:49.677 [info] == Migrated 20240101000005 in 0.0s
10:30:49.677 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:30:49.677 [info] create table custom_pk
10:30:49.678 [info] create table no_pk
10:30:49.678 [info] create table composite_pk
10:30:49.678 [info] == Migrated 20240101000006 in 0.0s
10:30:49.678 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:30:49.678 [info] create table timestamps
10:30:49.678 [info] == Migrated 20240101000007 in 0.0s
10:30:49.698 [info] Cleared entire schema cache
10:30:49.770 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:30:49.770 [info] create table users
10:30:49.771 [info] == Migrated 20240101000001 in 0.0s
10:30:49.771 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:30:49.771 [info] create table groups
10:30:49.771 [info] == Migrated 20240101000002 in 0.0s
10:30:49.771 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:30:49.771 [info] create table user_groups
10:30:49.771 [info] create index user_groups_user_id_group_id_index
10:30:49.771 [info] == Migrated 20240101000003 in 0.0s
10:30:49.772 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:30:49.772 [info] create table association_parents
10:30:49.772 [info] create table associations
10:30:49.772 [info] create table association_items
10:30:49.772 [info] == Migrated 20240101000004 in 0.0s
10:30:49.772 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:30:49.772 [info] create table basic_types
10:30:49.772 [info] == Migrated 20240101000005 in 0.0s
10:30:49.773 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:30:49.773 [info] create table custom_pk
10:30:49.773 [info] create table no_pk
10:30:49.773 [info] create table composite_pk
10:30:49.773 [info] == Migrated 20240101000006 in 0.0s
10:30:49.773 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:30:49.773 [info] create table timestamps
10:30:49.773 [info] == Migrated 20240101000007 in 0.0s
10:30:49.774 [info] Cleared entire schema cache
10:30:49.847 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:30:49.847 [info] create table users
10:30:49.848 [info] == Migrated 20240101000001 in 0.0s
10:30:49.848 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:30:49.848 [info] create table groups
10:30:49.848 [info] == Migrated 20240101000002 in 0.0s
10:30:49.848 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:30:49.848 [info] create table user_groups
10:30:49.848 [info] create index user_groups_user_id_group_id_index
10:30:49.848 [info] == Migrated 20240101000003 in 0.0s
10:30:49.849 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:30:49.849 [info] create table association_parents
10:30:49.849 [info] create table associations
10:30:49.849 [info] create table association_items
10:30:49.849 [info] == Migrated 20240101000004 in 0.0s
10:30:49.849 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:30:49.849 [info] create table basic_types
10:30:49.849 [info] == Migrated 20240101000005 in 0.0s
10:30:49.850 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:30:49.850 [info] create table custom_pk
10:30:49.850 [info] create table no_pk
10:30:49.850 [info] create table composite_pk
10:30:49.850 [info] == Migrated 20240101000006 in 0.0s
10:30:49.850 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:30:49.850 [info] create table timestamps
10:30:49.850 [info] == Migrated 20240101000007 in 0.0s
10:30:49.851 [info] Cleared entire schema cache
10:30:49.924 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:30:49.924 [info] create table users
10:30:49.924 [info] == Migrated 20240101000001 in 0.0s
10:30:49.925 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:30:49.925 [info] create table groups
10:30:49.925 [info] == Migrated 20240101000002 in 0.0s
10:30:49.925 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:30:49.925 [info] create table user_groups
10:30:49.925 [info] create index user_groups_user_id_group_id_index
10:30:49.925 [info] == Migrated 20240101000003 in 0.0s
10:30:49.926 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:30:49.926 [info] create table association_parents
10:30:49.926 [info] create table associations
10:30:49.926 [info] create table association_items
10:30:49.926 [info] == Migrated 20240101000004 in 0.0s
10:30:49.926 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:30:49.926 [info] create table basic_types
10:30:49.926 [info] == Migrated 20240101000005 in 0.0s
10:30:49.927 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:30:49.927 [info] create table custom_pk
10:30:49.927 [info] create table no_pk
10:30:49.927 [info] create table composite_pk
10:30:49.927 [info] == Migrated 20240101000006 in 0.0s
10:30:49.927 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:30:49.927 [info] create table timestamps
10:30:49.927 [info] == Migrated 20240101000007 in 0.0s
10:30:49.928 [info] Cleared entire schema cache
10:30:50.000 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:30:50.000 [info] create table users
10:30:50.000 [info] == Migrated 20240101000001 in 0.0s
10:30:50.001 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:30:50.001 [info] create table groups
10:30:50.001 [info] == Migrated 20240101000002 in 0.0s
10:30:50.001 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:30:50.001 [info] create table user_groups
10:30:50.001 [info] create index user_groups_user_id_group_id_index
10:30:50.001 [info] == Migrated 20240101000003 in 0.0s
10:30:50.002 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:30:50.002 [info] create table association_parents
10:30:50.002 [info] create table associations
10:30:50.002 [info] create table association_items
10:30:50.002 [info] == Migrated 20240101000004 in 0.0s
10:30:50.002 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:30:50.002 [info] create table basic_types
10:30:50.002 [info] == Migrated 20240101000005 in 0.0s
10:30:50.002 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:30:50.003 [info] create table custom_pk
10:30:50.003 [info] create table no_pk
10:30:50.003 [info] create table composite_pk
10:30:50.003 [info] == Migrated 20240101000006 in 0.0s
10:30:50.003 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:30:50.003 [info] create table timestamps
10:30:50.003 [info] == Migrated 20240101000007 in 0.0s
10:30:50.004 [info] Cleared entire schema cache
10:30:50.079 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:30:50.079 [info] create table users
10:30:50.079 [info] == Migrated 20240101000001 in 0.0s
10:30:50.080 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:30:50.080 [info] create table groups
10:30:50.080 [info] == Migrated 20240101000002 in 0.0s
10:30:50.080 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:30:50.080 [info] create table user_groups
10:30:50.080 [info] create index user_groups_user_id_group_id_index
10:30:50.080 [info] == Migrated 20240101000003 in 0.0s
10:30:50.080 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:30:50.081 [info] create table association_parents
10:30:50.081 [info] create table associations
10:30:50.081 [info] create table association_items
10:30:50.081 [info] == Migrated 20240101000004 in 0.0s
10:30:50.081 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:30:50.081 [info] create table basic_types
10:30:50.081 [info] == Migrated 20240101000005 in 0.0s
10:30:50.081 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:30:50.081 [info] create table custom_pk
10:30:50.081 [info] create table no_pk
10:30:50.081 [info] create table composite_pk
10:30:50.082 [info] == Migrated 20240101000006 in 0.0s
10:30:50.082 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:30:50.082 [info] create table timestamps
10:30:50.082 [info] == Migrated 20240101000007 in 0.0s
10:30:50.083 [info] Cleared entire schema cache
10:30:50.156 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:30:50.156 [info] create table users
10:30:50.156 [info] == Migrated 20240101000001 in 0.0s
10:30:50.156 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:30:50.156 [info] create table groups
10:30:50.156 [info] == Migrated 20240101000002 in 0.0s
10:30:50.157 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:30:50.157 [info] create table user_groups
10:30:50.157 [info] create index user_groups_user_id_group_id_index
10:30:50.157 [info] == Migrated 20240101000003 in 0.0s
10:30:50.157 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:30:50.157 [info] create table association_parents
10:30:50.157 [info] create table associations
10:30:50.157 [info] create table association_items
10:30:50.158 [info] == Migrated 20240101000004 in 0.0s
10:30:50.158 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:30:50.158 [info] create table basic_types
10:30:50.158 [info] == Migrated 20240101000005 in 0.0s
10:30:50.158 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:30:50.158 [info] create table custom_pk
10:30:50.158 [info] create table no_pk
10:30:50.158 [info] create table composite_pk
10:30:50.158 [info] == Migrated 20240101000006 in 0.0s
10:30:50.159 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:30:50.159 [info] create table timestamps
10:30:50.159 [info] == Migrated 20240101000007 in 0.0s
10:30:50.160 [info] Cleared entire schema cache
10:30:50.235 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:30:50.235 [info] create table users
10:30:50.235 [info] == Migrated 20240101000001 in 0.0s
10:30:50.236 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:30:50.236 [info] create table groups
10:30:50.236 [info] == Migrated 20240101000002 in 0.0s
10:30:50.236 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:30:50.236 [info] create table user_groups
10:30:50.236 [info] create index user_groups_user_id_group_id_index
10:30:50.236 [info] == Migrated 20240101000003 in 0.0s
10:30:50.236 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:30:50.236 [info] create table association_parents
10:30:50.237 [info] create table associations
10:30:50.237 [info] create table association_items
10:30:50.237 [info] == Migrated 20240101000004 in 0.0s
10:30:50.237 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:30:50.237 [info] create table basic_types
10:30:50.237 [info] == Migrated 20240101000005 in 0.0s
10:30:50.237 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:30:50.237 [info] create table custom_pk
10:30:50.237 [info] create table no_pk
10:30:50.237 [info] create table composite_pk
10:30:50.238 [info] == Migrated 20240101000006 in 0.0s
10:30:50.238 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:30:50.238 [info] create table timestamps
10:30:50.238 [info] == Migrated 20240101000007 in 0.0s
10:30:50.239 [info] Cleared entire schema cache
10:30:50.313 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:30:50.313 [info] create table users
10:30:50.313 [info] == Migrated 20240101000001 in 0.0s
10:30:50.313 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:30:50.313 [info] create table groups
10:30:50.313 [info] == Migrated 20240101000002 in 0.0s
10:30:50.314 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:30:50.314 [info] create table user_groups
10:30:50.314 [info] create index user_groups_user_id_group_id_index
10:30:50.314 [info] == Migrated 20240101000003 in 0.0s
10:30:50.314 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:30:50.314 [info] create table association_parents
10:30:50.314 [info] create table associations
10:30:50.314 [info] create table association_items
10:30:50.314 [info] == Migrated 20240101000004 in 0.0s
10:30:50.315 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:30:50.315 [info] create table basic_types
10:30:50.315 [info] == Migrated 20240101000005 in 0.0s
10:30:50.315 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:30:50.315 [info] create table custom_pk
10:30:50.315 [info] create table no_pk
10:30:50.315 [info] create table composite_pk
10:30:50.315 [info] == Migrated 20240101000006 in 0.0s
10:30:50.315 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:30:50.315 [info] create table timestamps
10:30:50.315 [info] == Migrated 20240101000007 in 0.0s
10:30:50.316 [info] Cleared entire schema cache
10:30:50.388 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:30:50.388 [info] create table users
10:30:50.389 [info] == Migrated 20240101000001 in 0.0s
10:30:50.389 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:30:50.389 [info] create table groups
10:30:50.389 [info] == Migrated 20240101000002 in 0.0s
10:30:50.389 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:30:50.389 [info] create table user_groups
10:30:50.390 [info] create index user_groups_user_id_group_id_index
10:30:50.390 [info] == Migrated 20240101000003 in 0.0s
10:30:50.390 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:30:50.390 [info] create table association_parents
10:30:50.390 [info] create table associations
10:30:50.390 [info] create table association_items
10:30:50.390 [info] == Migrated 20240101000004 in 0.0s
10:30:50.391 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:30:50.391 [info] create table basic_types
10:30:50.391 [info] == Migrated 20240101000005 in 0.0s
10:30:50.391 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:30:50.391 [info] create table custom_pk
10:30:50.391 [info] create table no_pk
10:30:50.391 [info] create table composite_pk
10:30:50.391 [info] == Migrated 20240101000006 in 0.0s
10:30:50.391 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:30:50.391 [info] create table timestamps
10:30:50.392 [info] == Migrated 20240101000007 in 0.0s
10:30:50.393 [info] Cleared entire schema cache
10:30:50.467 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:30:50.467 [info] create table users
10:30:50.467 [info] == Migrated 20240101000001 in 0.0s
10:30:50.467 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:30:50.468 [info] create table groups
10:30:50.468 [info] == Migrated 20240101000002 in 0.0s
10:30:50.468 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:30:50.468 [info] create table user_groups
10:30:50.468 [info] create index user_groups_user_id_group_id_index
10:30:50.468 [info] == Migrated 20240101000003 in 0.0s
10:30:50.468 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:30:50.468 [info] create table association_parents
10:30:50.469 [info] create table associations
10:30:50.469 [info] create table association_items
10:30:50.469 [info] == Migrated 20240101000004 in 0.0s
10:30:50.469 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:30:50.469 [info] create table basic_types
10:30:50.469 [info] == Migrated 20240101000005 in 0.0s
10:30:50.469 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:30:50.469 [info] create table custom_pk
10:30:50.469 [info] create table no_pk
10:30:50.469 [info] create table composite_pk
10:30:50.469 [info] == Migrated 20240101000006 in 0.0s
10:30:50.470 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:30:50.470 [info] create table timestamps
10:30:50.470 [info] == Migrated 20240101000007 in 0.0s
10:30:50.471 [info] Cleared entire schema cache
10:30:50.547 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:30:50.547 [info] create table users
10:30:50.547 [info] == Migrated 20240101000001 in 0.0s
10:30:50.548 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:30:50.548 [info] create table groups
10:30:50.548 [info] == Migrated 20240101000002 in 0.0s
10:30:50.548 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:30:50.548 [info] create table user_groups
10:30:50.548 [info] create index user_groups_user_id_group_id_index
10:30:50.548 [info] == Migrated 20240101000003 in 0.0s
10:30:50.549 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:30:50.549 [info] create table association_parents
10:30:50.549 [info] create table associations
10:30:50.549 [info] create table association_items
10:30:50.549 [info] == Migrated 20240101000004 in 0.0s
10:30:50.549 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:30:50.549 [info] create table basic_types
10:30:50.549 [info] == Migrated 20240101000005 in 0.0s
10:30:50.549 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:30:50.550 [info] create table custom_pk
10:30:50.550 [info] create table no_pk
10:30:50.550 [info] create table composite_pk
10:30:50.550 [info] == Migrated 20240101000006 in 0.0s
10:30:50.550 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:30:50.550 [info] create table timestamps
10:30:50.550 [info] == Migrated 20240101000007 in 0.0s
10:30:50.551 [info] Cleared entire schema cache
10:32:48.040 [info] Drops.Relation.SchemaCache started with persistent storage at /Users/<USER>/Workspace/drops-relation/tmp/cache/test/drops_schema/drops_schema_cache.dets
10:34:33.112 [info] Drops.Relation.SchemaCache started with persistent storage at /Users/<USER>/Workspace/drops-relation/tmp/cache/test/drops_schema/drops_schema_cache.dets
10:35:07.801 [info] Drops.Relation.SchemaCache started with persistent storage at /Users/<USER>/Workspace/drops-relation/tmp/cache/test/drops_schema/drops_schema_cache.dets
10:36:32.588 [info] Drops.Relation.SchemaCache started with persistent storage at /Users/<USER>/Workspace/drops-relation/tmp/cache/test/drops_schema/drops_schema_cache.dets
10:36:33.121 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:36:33.121 [info] create table users
10:36:33.122 [info] == Migrated 20240101000001 in 0.0s
10:36:33.131 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:36:33.131 [info] create table groups
10:36:33.131 [info] == Migrated 20240101000002 in 0.0s
10:36:33.131 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:36:33.132 [info] create table user_groups
10:36:33.132 [info] create index user_groups_user_id_group_id_index
10:36:33.132 [info] == Migrated 20240101000003 in 0.0s
10:36:33.132 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:36:33.132 [info] create table association_parents
10:36:33.132 [info] create table associations
10:36:33.132 [info] create table association_items
10:36:33.132 [info] == Migrated 20240101000004 in 0.0s
10:36:33.133 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:36:33.133 [info] create table basic_types
10:36:33.136 [info] == Migrated 20240101000005 in 0.0s
10:36:33.136 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:36:33.136 [info] create table custom_pk
10:36:33.136 [info] create table no_pk
10:36:33.136 [info] create table composite_pk
10:36:33.137 [info] == Migrated 20240101000006 in 0.0s
10:36:33.137 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:36:33.137 [info] create table timestamps
10:36:33.137 [info] == Migrated 20240101000007 in 0.0s
10:36:33.166 [info] Cleared entire schema cache
10:37:18.003 [info] Drops.Relation.SchemaCache started with persistent storage at /Users/<USER>/Workspace/drops-relation/tmp/cache/test/drops_schema/drops_schema_cache.dets
10:37:18.534 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:37:18.534 [info] create table users
10:37:18.535 [info] == Migrated 20240101000001 in 0.0s
10:37:18.543 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:37:18.543 [info] create table groups
10:37:18.543 [info] == Migrated 20240101000002 in 0.0s
10:37:18.543 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:37:18.543 [info] create table user_groups
10:37:18.544 [info] create index user_groups_user_id_group_id_index
10:37:18.544 [info] == Migrated 20240101000003 in 0.0s
10:37:18.544 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:37:18.544 [info] create table association_parents
10:37:18.544 [info] create table associations
10:37:18.544 [info] create table association_items
10:37:18.544 [info] == Migrated 20240101000004 in 0.0s
10:37:18.544 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:37:18.544 [info] create table basic_types
10:37:18.548 [info] == Migrated 20240101000005 in 0.0s
10:37:18.548 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:37:18.548 [info] create table custom_pk
10:37:18.548 [info] create table no_pk
10:37:18.548 [info] create table composite_pk
10:37:18.548 [info] == Migrated 20240101000006 in 0.0s
10:37:18.548 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:37:18.548 [info] create table timestamps
10:37:18.548 [info] == Migrated 20240101000007 in 0.0s
10:37:18.574 [info] Cleared entire schema cache
10:37:47.412 [info] Drops.Relation.SchemaCache started with persistent storage at /Users/<USER>/Workspace/drops-relation/tmp/cache/test/drops_schema/drops_schema_cache.dets
10:37:47.961 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:37:47.961 [info] create table users
10:37:47.962 [info] == Migrated 20240101000001 in 0.0s
10:37:47.971 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:37:47.971 [info] create table groups
10:37:47.971 [info] == Migrated 20240101000002 in 0.0s
10:37:47.971 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:37:47.971 [info] create table user_groups
10:37:47.971 [info] create index user_groups_user_id_group_id_index
10:37:47.971 [info] == Migrated 20240101000003 in 0.0s
10:37:47.972 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:37:47.972 [info] create table association_parents
10:37:47.972 [info] create table associations
10:37:47.972 [info] create table association_items
10:37:47.972 [info] == Migrated 20240101000004 in 0.0s
10:37:47.972 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:37:47.972 [info] create table basic_types
10:37:47.976 [info] == Migrated 20240101000005 in 0.0s
10:37:47.976 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:37:47.976 [info] create table custom_pk
10:37:47.976 [info] create table no_pk
10:37:47.976 [info] create table composite_pk
10:37:47.976 [info] == Migrated 20240101000006 in 0.0s
10:37:47.977 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:37:47.977 [info] create table timestamps
10:37:47.977 [info] == Migrated 20240101000007 in 0.0s
10:37:58.055 [info] Drops.Relation.SchemaCache started with persistent storage at /Users/<USER>/Workspace/drops-relation/tmp/cache/test/drops_schema/drops_schema_cache.dets
10:37:58.569 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:37:58.569 [info] create table users
10:37:58.570 [info] == Migrated 20240101000001 in 0.0s
10:37:58.578 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:37:58.578 [info] create table groups
10:37:58.578 [info] == Migrated 20240101000002 in 0.0s
10:37:58.578 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:37:58.578 [info] create table user_groups
10:37:58.578 [info] create index user_groups_user_id_group_id_index
10:37:58.578 [info] == Migrated 20240101000003 in 0.0s
10:37:58.579 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:37:58.579 [info] create table association_parents
10:37:58.579 [info] create table associations
10:37:58.579 [info] create table association_items
10:37:58.579 [info] == Migrated 20240101000004 in 0.0s
10:37:58.579 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:37:58.579 [info] create table basic_types
10:37:58.583 [info] == Migrated 20240101000005 in 0.0s
10:37:58.583 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:37:58.583 [info] create table custom_pk
10:37:58.583 [info] create table no_pk
10:37:58.583 [info] create table composite_pk
10:37:58.583 [info] == Migrated 20240101000006 in 0.0s
10:37:58.584 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:37:58.584 [info] create table timestamps
10:37:58.584 [info] == Migrated 20240101000007 in 0.0s
10:38:29.870 [info] Drops.Relation.SchemaCache started with persistent storage at /Users/<USER>/Workspace/drops-relation/tmp/cache/test/drops_schema/drops_schema_cache.dets
10:38:30.387 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:38:30.387 [info] create table users
10:38:30.388 [info] == Migrated 20240101000001 in 0.0s
10:38:30.396 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:38:30.396 [info] create table groups
10:38:30.396 [info] == Migrated 20240101000002 in 0.0s
10:38:30.396 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:38:30.396 [info] create table user_groups
10:38:30.396 [info] create index user_groups_user_id_group_id_index
10:38:30.397 [info] == Migrated 20240101000003 in 0.0s
10:38:30.397 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:38:30.397 [info] create table association_parents
10:38:30.397 [info] create table associations
10:38:30.397 [info] create table association_items
10:38:30.398 [info] == Migrated 20240101000004 in 0.0s
10:38:30.398 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:38:30.398 [info] create table basic_types
10:38:30.402 [info] == Migrated 20240101000005 in 0.0s
10:38:30.402 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:38:30.402 [info] create table custom_pk
10:38:30.402 [info] create table no_pk
10:38:30.402 [info] create table composite_pk
10:38:30.402 [info] == Migrated 20240101000006 in 0.0s
10:38:30.403 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:38:30.403 [info] create table timestamps
10:38:30.403 [info] == Migrated 20240101000007 in 0.0s
10:39:02.975 [info] Drops.Relation.SchemaCache started with persistent storage at /Users/<USER>/Workspace/drops-relation/tmp/cache/test/drops_schema/drops_schema_cache.dets
10:39:03.482 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:39:03.482 [info] create table users
10:39:03.483 [info] == Migrated 20240101000001 in 0.0s
10:39:03.490 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:39:03.490 [info] create table groups
10:39:03.490 [info] == Migrated 20240101000002 in 0.0s
10:39:03.490 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:39:03.490 [info] create table user_groups
10:39:03.490 [info] create index user_groups_user_id_group_id_index
10:39:03.490 [info] == Migrated 20240101000003 in 0.0s
10:39:03.491 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:39:03.491 [info] create table association_parents
10:39:03.491 [info] create table associations
10:39:03.491 [info] create table association_items
10:39:03.491 [info] == Migrated 20240101000004 in 0.0s
10:39:03.491 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:39:03.491 [info] create table basic_types
10:39:03.494 [info] == Migrated 20240101000005 in 0.0s
10:39:03.495 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:39:03.495 [info] create table custom_pk
10:39:03.495 [info] create table no_pk
10:39:03.495 [info] create table composite_pk
10:39:03.495 [info] == Migrated 20240101000006 in 0.0s
10:39:03.495 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:39:03.495 [info] create table timestamps
10:39:03.495 [info] == Migrated 20240101000007 in 0.0s
10:40:34.554 [info] Drops.Relation.SchemaCache started with persistent storage at /Users/<USER>/Workspace/drops-relation/tmp/cache/test/drops_schema/drops_schema_cache.dets
10:40:35.083 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:40:35.083 [info] create table users
10:40:35.084 [info] == Migrated 20240101000001 in 0.0s
10:40:35.093 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:40:35.093 [info] create table groups
10:40:35.093 [info] == Migrated 20240101000002 in 0.0s
10:40:35.093 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:40:35.094 [info] create table user_groups
10:40:35.094 [info] create index user_groups_user_id_group_id_index
10:40:35.094 [info] == Migrated 20240101000003 in 0.0s
10:40:35.094 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:40:35.094 [info] create table association_parents
10:40:35.094 [info] create table associations
10:40:35.094 [info] create table association_items
10:40:35.094 [info] == Migrated 20240101000004 in 0.0s
10:40:35.094 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:40:35.094 [info] create table basic_types
10:40:35.098 [info] == Migrated 20240101000005 in 0.0s
10:40:35.098 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:40:35.098 [info] create table custom_pk
10:40:35.098 [info] create table no_pk
10:40:35.098 [info] create table composite_pk
10:40:35.098 [info] == Migrated 20240101000006 in 0.0s
10:40:35.099 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:40:35.099 [info] create table timestamps
10:40:35.099 [info] == Migrated 20240101000007 in 0.0s
10:40:57.317 [info] Drops.Relation.SchemaCache started with persistent storage at /Users/<USER>/Workspace/drops-relation/tmp/cache/test/drops_schema/drops_schema_cache.dets
10:40:57.829 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:40:57.829 [info] create table users
10:40:57.829 [info] == Migrated 20240101000001 in 0.0s
10:40:57.837 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:40:57.837 [info] create table groups
10:40:57.837 [info] == Migrated 20240101000002 in 0.0s
10:40:57.837 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:40:57.837 [info] create table user_groups
10:40:57.837 [info] create index user_groups_user_id_group_id_index
10:40:57.837 [info] == Migrated 20240101000003 in 0.0s
10:40:57.838 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:40:57.838 [info] create table association_parents
10:40:57.838 [info] create table associations
10:40:57.838 [info] create table association_items
10:40:57.838 [info] == Migrated 20240101000004 in 0.0s
10:40:57.838 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:40:57.838 [info] create table basic_types
10:40:57.841 [info] == Migrated 20240101000005 in 0.0s
10:40:57.842 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:40:57.842 [info] create table custom_pk
10:40:57.842 [info] create table no_pk
10:40:57.842 [info] create table composite_pk
10:40:57.842 [info] == Migrated 20240101000006 in 0.0s
10:40:57.842 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:40:57.842 [info] create table timestamps
10:40:57.842 [info] == Migrated 20240101000007 in 0.0s
10:40:57.871 [info] Cleared entire schema cache
10:41:30.107 [info] Drops.Relation.SchemaCache started with persistent storage at /Users/<USER>/Workspace/drops-relation/tmp/cache/test/drops_schema/drops_schema_cache.dets
10:41:30.646 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:30.646 [info] create table users
10:41:30.647 [info] == Migrated 20240101000001 in 0.0s
10:41:30.655 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:30.655 [info] create table groups
10:41:30.655 [info] == Migrated 20240101000002 in 0.0s
10:41:30.655 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:30.655 [info] create table user_groups
10:41:30.655 [info] create index user_groups_user_id_group_id_index
10:41:30.655 [info] == Migrated 20240101000003 in 0.0s
10:41:30.656 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:30.656 [info] create table association_parents
10:41:30.656 [info] create table associations
10:41:30.656 [info] create table association_items
10:41:30.656 [info] == Migrated 20240101000004 in 0.0s
10:41:30.656 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:30.656 [info] create table basic_types
10:41:30.659 [info] == Migrated 20240101000005 in 0.0s
10:41:30.660 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:30.660 [info] create table custom_pk
10:41:30.660 [info] create table no_pk
10:41:30.660 [info] create table composite_pk
10:41:30.660 [info] == Migrated 20240101000006 in 0.0s
10:41:30.660 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:30.660 [info] create table timestamps
10:41:30.660 [info] == Migrated 20240101000007 in 0.0s
10:41:30.686 [info] Cleared entire schema cache
10:41:33.433 [info] Drops.Relation.SchemaCache started with persistent storage at /Users/<USER>/Workspace/drops-relation/tmp/cache/test/drops_schema/drops_schema_cache.dets
10:41:33.943 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:33.943 [info] create table users
10:41:33.944 [info] == Migrated 20240101000001 in 0.0s
10:41:33.951 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:33.951 [info] create table groups
10:41:33.951 [info] == Migrated 20240101000002 in 0.0s
10:41:33.951 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:33.951 [info] create table user_groups
10:41:33.952 [info] create index user_groups_user_id_group_id_index
10:41:33.952 [info] == Migrated 20240101000003 in 0.0s
10:41:33.952 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:33.952 [info] create table association_parents
10:41:33.952 [info] create table associations
10:41:33.952 [info] create table association_items
10:41:33.952 [info] == Migrated 20240101000004 in 0.0s
10:41:33.953 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:33.953 [info] create table basic_types
10:41:33.956 [info] == Migrated 20240101000005 in 0.0s
10:41:33.956 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:33.956 [info] create table custom_pk
10:41:33.956 [info] create table no_pk
10:41:33.956 [info] create table composite_pk
10:41:33.957 [info] == Migrated 20240101000006 in 0.0s
10:41:33.957 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:33.957 [info] create table timestamps
10:41:33.957 [info] == Migrated 20240101000007 in 0.0s
10:41:33.983 [info] Cleared entire schema cache
10:41:34.048 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:34.049 [info] create table users
10:41:34.049 [info] == Migrated 20240101000001 in 0.0s
10:41:34.049 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:34.049 [info] create table groups
10:41:34.049 [info] == Migrated 20240101000002 in 0.0s
10:41:34.049 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:34.049 [info] create table user_groups
10:41:34.050 [info] create index user_groups_user_id_group_id_index
10:41:34.050 [info] == Migrated 20240101000003 in 0.0s
10:41:34.050 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:34.050 [info] create table association_parents
10:41:34.050 [info] create table associations
10:41:34.050 [info] create table association_items
10:41:34.050 [info] == Migrated 20240101000004 in 0.0s
10:41:34.051 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:34.051 [info] create table basic_types
10:41:34.051 [info] == Migrated 20240101000005 in 0.0s
10:41:34.051 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:34.051 [info] create table custom_pk
10:41:34.051 [info] create table no_pk
10:41:34.051 [info] create table composite_pk
10:41:34.051 [info] == Migrated 20240101000006 in 0.0s
10:41:34.051 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:34.051 [info] create table timestamps
10:41:34.051 [info] == Migrated 20240101000007 in 0.0s
10:41:34.052 [info] Cleared entire schema cache
10:41:34.128 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:34.129 [info] create table users
10:41:34.129 [info] == Migrated 20240101000001 in 0.0s
10:41:34.129 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:34.129 [info] create table groups
10:41:34.129 [info] == Migrated 20240101000002 in 0.0s
10:41:34.129 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:34.129 [info] create table user_groups
10:41:34.130 [info] create index user_groups_user_id_group_id_index
10:41:34.130 [info] == Migrated 20240101000003 in 0.0s
10:41:34.130 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:34.130 [info] create table association_parents
10:41:34.130 [info] create table associations
10:41:34.130 [info] create table association_items
10:41:34.130 [info] == Migrated 20240101000004 in 0.0s
10:41:34.130 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:34.130 [info] create table basic_types
10:41:34.130 [info] == Migrated 20240101000005 in 0.0s
10:41:34.131 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:34.131 [info] create table custom_pk
10:41:34.131 [info] create table no_pk
10:41:34.131 [info] create table composite_pk
10:41:34.131 [info] == Migrated 20240101000006 in 0.0s
10:41:34.131 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:34.131 [info] create table timestamps
10:41:34.131 [info] == Migrated 20240101000007 in 0.0s
10:41:34.132 [info] Cleared entire schema cache
10:41:34.206 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:34.206 [info] create table users
10:41:34.206 [info] == Migrated 20240101000001 in 0.0s
10:41:34.207 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:34.207 [info] create table groups
10:41:34.207 [info] == Migrated 20240101000002 in 0.0s
10:41:34.207 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:34.207 [info] create table user_groups
10:41:34.207 [info] create index user_groups_user_id_group_id_index
10:41:34.207 [info] == Migrated 20240101000003 in 0.0s
10:41:34.208 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:34.208 [info] create table association_parents
10:41:34.208 [info] create table associations
10:41:34.208 [info] create table association_items
10:41:34.208 [info] == Migrated 20240101000004 in 0.0s
10:41:34.208 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:34.208 [info] create table basic_types
10:41:34.208 [info] == Migrated 20240101000005 in 0.0s
10:41:34.208 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:34.209 [info] create table custom_pk
10:41:34.209 [info] create table no_pk
10:41:34.209 [info] create table composite_pk
10:41:34.209 [info] == Migrated 20240101000006 in 0.0s
10:41:34.209 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:34.209 [info] create table timestamps
10:41:34.209 [info] == Migrated 20240101000007 in 0.0s
10:41:34.210 [info] Cleared entire schema cache
10:41:34.286 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:34.286 [info] create table users
10:41:34.286 [info] == Migrated 20240101000001 in 0.0s
10:41:34.286 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:34.286 [info] create table groups
10:41:34.286 [info] == Migrated 20240101000002 in 0.0s
10:41:34.287 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:34.287 [info] create table user_groups
10:41:34.287 [info] create index user_groups_user_id_group_id_index
10:41:34.287 [info] == Migrated 20240101000003 in 0.0s
10:41:34.287 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:34.287 [info] create table association_parents
10:41:34.287 [info] create table associations
10:41:34.287 [info] create table association_items
10:41:34.287 [info] == Migrated 20240101000004 in 0.0s
10:41:34.288 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:34.288 [info] create table basic_types
10:41:34.288 [info] == Migrated 20240101000005 in 0.0s
10:41:34.288 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:34.288 [info] create table custom_pk
10:41:34.288 [info] create table no_pk
10:41:34.288 [info] create table composite_pk
10:41:34.288 [info] == Migrated 20240101000006 in 0.0s
10:41:34.288 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:34.289 [info] create table timestamps
10:41:34.289 [info] == Migrated 20240101000007 in 0.0s
10:41:34.308 [info] Cleared entire schema cache
10:41:34.363 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:34.363 [info] create table users
10:41:34.364 [info] == Migrated 20240101000001 in 0.0s
10:41:34.364 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:34.364 [info] create table groups
10:41:34.364 [info] == Migrated 20240101000002 in 0.0s
10:41:34.364 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:34.364 [info] create table user_groups
10:41:34.364 [info] create index user_groups_user_id_group_id_index
10:41:34.365 [info] == Migrated 20240101000003 in 0.0s
10:41:34.365 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:34.365 [info] create table association_parents
10:41:34.365 [info] create table associations
10:41:34.365 [info] create table association_items
10:41:34.365 [info] == Migrated 20240101000004 in 0.0s
10:41:34.365 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:34.365 [info] create table basic_types
10:41:34.365 [info] == Migrated 20240101000005 in 0.0s
10:41:34.366 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:34.366 [info] create table custom_pk
10:41:34.366 [info] create table no_pk
10:41:34.366 [info] create table composite_pk
10:41:34.366 [info] == Migrated 20240101000006 in 0.0s
10:41:34.366 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:34.366 [info] create table timestamps
10:41:34.366 [info] == Migrated 20240101000007 in 0.0s
10:41:34.368 [info] Cleared entire schema cache
10:41:34.443 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:34.443 [info] create table users
10:41:34.443 [info] == Migrated 20240101000001 in 0.0s
10:41:34.444 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:34.444 [info] create table groups
10:41:34.444 [info] == Migrated 20240101000002 in 0.0s
10:41:34.444 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:34.444 [info] create table user_groups
10:41:34.444 [info] create index user_groups_user_id_group_id_index
10:41:34.444 [info] == Migrated 20240101000003 in 0.0s
10:41:34.445 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:34.445 [info] create table association_parents
10:41:34.445 [info] create table associations
10:41:34.445 [info] create table association_items
10:41:34.445 [info] == Migrated 20240101000004 in 0.0s
10:41:34.445 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:34.445 [info] create table basic_types
10:41:34.445 [info] == Migrated 20240101000005 in 0.0s
10:41:34.446 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:34.446 [info] create table custom_pk
10:41:34.446 [info] create table no_pk
10:41:34.446 [info] create table composite_pk
10:41:34.446 [info] == Migrated 20240101000006 in 0.0s
10:41:34.446 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:34.446 [info] create table timestamps
10:41:34.446 [info] == Migrated 20240101000007 in 0.0s
10:41:34.447 [info] Cleared entire schema cache
10:41:34.522 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:34.522 [info] create table users
10:41:34.522 [info] == Migrated 20240101000001 in 0.0s
10:41:34.523 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:34.523 [info] create table groups
10:41:34.523 [info] == Migrated 20240101000002 in 0.0s
10:41:34.523 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:34.523 [info] create table user_groups
10:41:34.523 [info] create index user_groups_user_id_group_id_index
10:41:34.523 [info] == Migrated 20240101000003 in 0.0s
10:41:34.523 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:34.523 [info] create table association_parents
10:41:34.524 [info] create table associations
10:41:34.524 [info] create table association_items
10:41:34.524 [info] == Migrated 20240101000004 in 0.0s
10:41:34.524 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:34.524 [info] create table basic_types
10:41:34.524 [info] == Migrated 20240101000005 in 0.0s
10:41:34.524 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:34.524 [info] create table custom_pk
10:41:34.524 [info] create table no_pk
10:41:34.524 [info] create table composite_pk
10:41:34.525 [info] == Migrated 20240101000006 in 0.0s
10:41:34.525 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:34.525 [info] create table timestamps
10:41:34.525 [info] == Migrated 20240101000007 in 0.0s
10:41:34.526 [info] Cleared entire schema cache
10:41:34.605 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:34.605 [info] create table users
10:41:34.605 [info] == Migrated 20240101000001 in 0.0s
10:41:34.606 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:34.606 [info] create table groups
10:41:34.606 [info] == Migrated 20240101000002 in 0.0s
10:41:34.606 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:34.606 [info] create table user_groups
10:41:34.606 [info] create index user_groups_user_id_group_id_index
10:41:34.606 [info] == Migrated 20240101000003 in 0.0s
10:41:34.607 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:34.607 [info] create table association_parents
10:41:34.607 [info] create table associations
10:41:34.607 [info] create table association_items
10:41:34.607 [info] == Migrated 20240101000004 in 0.0s
10:41:34.607 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:34.607 [info] create table basic_types
10:41:34.607 [info] == Migrated 20240101000005 in 0.0s
10:41:34.608 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:34.608 [info] create table custom_pk
10:41:34.608 [info] create table no_pk
10:41:34.608 [info] create table composite_pk
10:41:34.608 [info] == Migrated 20240101000006 in 0.0s
10:41:34.608 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:34.608 [info] create table timestamps
10:41:34.609 [info] == Migrated 20240101000007 in 0.0s
10:41:34.610 [info] Cleared entire schema cache
10:41:34.685 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:34.685 [info] create table users
10:41:34.685 [info] == Migrated 20240101000001 in 0.0s
10:41:34.685 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:34.685 [info] create table groups
10:41:34.685 [info] == Migrated 20240101000002 in 0.0s
10:41:34.686 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:34.686 [info] create table user_groups
10:41:34.686 [info] create index user_groups_user_id_group_id_index
10:41:34.686 [info] == Migrated 20240101000003 in 0.0s
10:41:34.686 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:34.686 [info] create table association_parents
10:41:34.686 [info] create table associations
10:41:34.686 [info] create table association_items
10:41:34.687 [info] == Migrated 20240101000004 in 0.0s
10:41:34.687 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:34.687 [info] create table basic_types
10:41:34.687 [info] == Migrated 20240101000005 in 0.0s
10:41:34.687 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:34.687 [info] create table custom_pk
10:41:34.687 [info] create table no_pk
10:41:34.687 [info] create table composite_pk
10:41:34.687 [info] == Migrated 20240101000006 in 0.0s
10:41:34.688 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:34.688 [info] create table timestamps
10:41:34.688 [info] == Migrated 20240101000007 in 0.0s
10:41:34.689 [info] Cleared entire schema cache
10:41:34.764 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:34.764 [info] create table users
10:41:34.764 [info] == Migrated 20240101000001 in 0.0s
10:41:34.764 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:34.764 [info] create table groups
10:41:34.764 [info] == Migrated 20240101000002 in 0.0s
10:41:34.764 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:34.764 [info] create table user_groups
10:41:34.765 [info] create index user_groups_user_id_group_id_index
10:41:34.765 [info] == Migrated 20240101000003 in 0.0s
10:41:34.765 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:34.765 [info] create table association_parents
10:41:34.765 [info] create table associations
10:41:34.765 [info] create table association_items
10:41:34.765 [info] == Migrated 20240101000004 in 0.0s
10:41:34.765 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:34.765 [info] create table basic_types
10:41:34.766 [info] == Migrated 20240101000005 in 0.0s
10:41:34.766 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:34.766 [info] create table custom_pk
10:41:34.766 [info] create table no_pk
10:41:34.766 [info] create table composite_pk
10:41:34.766 [info] == Migrated 20240101000006 in 0.0s
10:41:34.766 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:34.766 [info] create table timestamps
10:41:34.766 [info] == Migrated 20240101000007 in 0.0s
10:41:34.768 [info] Cleared entire schema cache
10:41:34.843 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:34.843 [info] create table users
10:41:34.843 [info] == Migrated 20240101000001 in 0.0s
10:41:34.844 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:34.844 [info] create table groups
10:41:34.844 [info] == Migrated 20240101000002 in 0.0s
10:41:34.844 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:34.844 [info] create table user_groups
10:41:34.844 [info] create index user_groups_user_id_group_id_index
10:41:34.844 [info] == Migrated 20240101000003 in 0.0s
10:41:34.845 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:34.845 [info] create table association_parents
10:41:34.845 [info] create table associations
10:41:34.845 [info] create table association_items
10:41:34.845 [info] == Migrated 20240101000004 in 0.0s
10:41:34.845 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:34.845 [info] create table basic_types
10:41:34.845 [info] == Migrated 20240101000005 in 0.0s
10:41:34.845 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:34.846 [info] create table custom_pk
10:41:34.846 [info] create table no_pk
10:41:34.846 [info] create table composite_pk
10:41:34.846 [info] == Migrated 20240101000006 in 0.0s
10:41:34.846 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:34.846 [info] create table timestamps
10:41:34.846 [info] == Migrated 20240101000007 in 0.0s
10:41:34.847 [info] Cleared entire schema cache
10:41:50.039 [info] Drops.Relation.SchemaCache started with persistent storage at /Users/<USER>/Workspace/drops-relation/tmp/cache/test/drops_schema/drops_schema_cache.dets
10:41:50.614 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:50.614 [info] create table users
10:41:50.615 [info] == Migrated 20240101000001 in 0.0s
10:41:50.624 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:50.624 [info] create table groups
10:41:50.624 [info] == Migrated 20240101000002 in 0.0s
10:41:50.624 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:50.624 [info] create table user_groups
10:41:50.624 [info] create index user_groups_user_id_group_id_index
10:41:50.624 [info] == Migrated 20240101000003 in 0.0s
10:41:50.625 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:50.625 [info] create table association_parents
10:41:50.625 [info] create table associations
10:41:50.625 [info] create table association_items
10:41:50.625 [info] == Migrated 20240101000004 in 0.0s
10:41:50.625 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:50.625 [info] create table basic_types
10:41:50.628 [info] == Migrated 20240101000005 in 0.0s
10:41:50.629 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:50.629 [info] create table custom_pk
10:41:50.629 [info] create table no_pk
10:41:50.629 [info] create table composite_pk
10:41:50.629 [info] == Migrated 20240101000006 in 0.0s
10:41:50.629 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:50.629 [info] create table timestamps
10:41:50.629 [info] == Migrated 20240101000007 in 0.0s
10:41:50.656 [info] Cleared entire schema cache
10:41:50.720 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:50.720 [info] create table users
10:41:50.720 [info] == Migrated 20240101000001 in 0.0s
10:41:50.720 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:50.720 [info] create table groups
10:41:50.720 [info] == Migrated 20240101000002 in 0.0s
10:41:50.721 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:50.721 [info] create table user_groups
10:41:50.721 [info] create index user_groups_user_id_group_id_index
10:41:50.721 [info] == Migrated 20240101000003 in 0.0s
10:41:50.721 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:50.721 [info] create table association_parents
10:41:50.721 [info] create table associations
10:41:50.722 [info] create table association_items
10:41:50.722 [info] == Migrated 20240101000004 in 0.0s
10:41:50.722 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:50.722 [info] create table basic_types
10:41:50.722 [info] == Migrated 20240101000005 in 0.0s
10:41:50.722 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:50.722 [info] create table custom_pk
10:41:50.722 [info] create table no_pk
10:41:50.722 [info] create table composite_pk
10:41:50.722 [info] == Migrated 20240101000006 in 0.0s
10:41:50.723 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:50.723 [info] create table timestamps
10:41:50.723 [info] == Migrated 20240101000007 in 0.0s
10:41:50.724 [info] Cleared entire schema cache
10:41:50.798 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:50.799 [info] create table users
10:41:50.799 [info] == Migrated 20240101000001 in 0.0s
10:41:50.799 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:50.799 [info] create table groups
10:41:50.799 [info] == Migrated 20240101000002 in 0.0s
10:41:50.799 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:50.800 [info] create table user_groups
10:41:50.800 [info] create index user_groups_user_id_group_id_index
10:41:50.800 [info] == Migrated 20240101000003 in 0.0s
10:41:50.800 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:50.800 [info] create table association_parents
10:41:50.800 [info] create table associations
10:41:50.800 [info] create table association_items
10:41:50.800 [info] == Migrated 20240101000004 in 0.0s
10:41:50.801 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:50.801 [info] create table basic_types
10:41:50.801 [info] == Migrated 20240101000005 in 0.0s
10:41:50.801 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:50.801 [info] create table custom_pk
10:41:50.801 [info] create table no_pk
10:41:50.801 [info] create table composite_pk
10:41:50.801 [info] == Migrated 20240101000006 in 0.0s
10:41:50.802 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:50.802 [info] create table timestamps
10:41:50.802 [info] == Migrated 20240101000007 in 0.0s
10:41:50.803 [info] Cleared entire schema cache
10:41:50.875 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:50.875 [info] create table users
10:41:50.875 [info] == Migrated 20240101000001 in 0.0s
10:41:50.875 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:50.875 [info] create table groups
10:41:50.875 [info] == Migrated 20240101000002 in 0.0s
10:41:50.876 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:50.876 [info] create table user_groups
10:41:50.876 [info] create index user_groups_user_id_group_id_index
10:41:50.876 [info] == Migrated 20240101000003 in 0.0s
10:41:50.876 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:50.876 [info] create table association_parents
10:41:50.876 [info] create table associations
10:41:50.876 [info] create table association_items
10:41:50.876 [info] == Migrated 20240101000004 in 0.0s
10:41:50.877 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:50.877 [info] create table basic_types
10:41:50.877 [info] == Migrated 20240101000005 in 0.0s
10:41:50.877 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:50.877 [info] create table custom_pk
10:41:50.877 [info] create table no_pk
10:41:50.877 [info] create table composite_pk
10:41:50.877 [info] == Migrated 20240101000006 in 0.0s
10:41:50.877 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:50.877 [info] create table timestamps
10:41:50.877 [info] == Migrated 20240101000007 in 0.0s
10:41:50.879 [info] Cleared entire schema cache
10:41:50.951 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:50.951 [info] create table users
10:41:50.951 [info] == Migrated 20240101000001 in 0.0s
10:41:50.951 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:50.951 [info] create table groups
10:41:50.951 [info] == Migrated 20240101000002 in 0.0s
10:41:50.952 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:50.952 [info] create table user_groups
10:41:50.952 [info] create index user_groups_user_id_group_id_index
10:41:50.952 [info] == Migrated 20240101000003 in 0.0s
10:41:50.952 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:50.952 [info] create table association_parents
10:41:50.952 [info] create table associations
10:41:50.952 [info] create table association_items
10:41:50.952 [info] == Migrated 20240101000004 in 0.0s
10:41:50.953 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:50.953 [info] create table basic_types
10:41:50.953 [info] == Migrated 20240101000005 in 0.0s
10:41:50.953 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:50.953 [info] create table custom_pk
10:41:50.953 [info] create table no_pk
10:41:50.953 [info] create table composite_pk
10:41:50.953 [info] == Migrated 20240101000006 in 0.0s
10:41:50.953 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:50.953 [info] create table timestamps
10:41:50.954 [info] == Migrated 20240101000007 in 0.0s
10:41:50.955 [info] Cleared entire schema cache
10:41:51.026 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:51.026 [info] create table users
10:41:51.027 [info] == Migrated 20240101000001 in 0.0s
10:41:51.027 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:51.027 [info] create table groups
10:41:51.027 [info] == Migrated 20240101000002 in 0.0s
10:41:51.027 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:51.027 [info] create table user_groups
10:41:51.027 [info] create index user_groups_user_id_group_id_index
10:41:51.028 [info] == Migrated 20240101000003 in 0.0s
10:41:51.028 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:51.028 [info] create table association_parents
10:41:51.028 [info] create table associations
10:41:51.028 [info] create table association_items
10:41:51.028 [info] == Migrated 20240101000004 in 0.0s
10:41:51.028 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:51.028 [info] create table basic_types
10:41:51.028 [info] == Migrated 20240101000005 in 0.0s
10:41:51.029 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:51.029 [info] create table custom_pk
10:41:51.029 [info] create table no_pk
10:41:51.029 [info] create table composite_pk
10:41:51.029 [info] == Migrated 20240101000006 in 0.0s
10:41:51.029 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:51.029 [info] create table timestamps
10:41:51.029 [info] == Migrated 20240101000007 in 0.0s
10:41:51.030 [info] Cleared entire schema cache
10:41:51.102 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:51.103 [info] create table users
10:41:51.103 [info] == Migrated 20240101000001 in 0.0s
10:41:51.103 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:51.103 [info] create table groups
10:41:51.103 [info] == Migrated 20240101000002 in 0.0s
10:41:51.103 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:51.103 [info] create table user_groups
10:41:51.103 [info] create index user_groups_user_id_group_id_index
10:41:51.104 [info] == Migrated 20240101000003 in 0.0s
10:41:51.104 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:51.104 [info] create table association_parents
10:41:51.104 [info] create table associations
10:41:51.104 [info] create table association_items
10:41:51.104 [info] == Migrated 20240101000004 in 0.0s
10:41:51.104 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:51.104 [info] create table basic_types
10:41:51.104 [info] == Migrated 20240101000005 in 0.0s
10:41:51.105 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:51.105 [info] create table custom_pk
10:41:51.105 [info] create table no_pk
10:41:51.105 [info] create table composite_pk
10:41:51.105 [info] == Migrated 20240101000006 in 0.0s
10:41:51.105 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:51.105 [info] create table timestamps
10:41:51.105 [info] == Migrated 20240101000007 in 0.0s
10:41:51.124 [info] Cleared entire schema cache
10:41:51.178 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:51.178 [info] create table users
10:41:51.178 [info] == Migrated 20240101000001 in 0.0s
10:41:51.179 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:51.179 [info] create table groups
10:41:51.179 [info] == Migrated 20240101000002 in 0.0s
10:41:51.179 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:51.179 [info] create table user_groups
10:41:51.179 [info] create index user_groups_user_id_group_id_index
10:41:51.179 [info] == Migrated 20240101000003 in 0.0s
10:41:51.179 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:51.180 [info] create table association_parents
10:41:51.180 [info] create table associations
10:41:51.180 [info] create table association_items
10:41:51.180 [info] == Migrated 20240101000004 in 0.0s
10:41:51.180 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:51.180 [info] create table basic_types
10:41:51.180 [info] == Migrated 20240101000005 in 0.0s
10:41:51.180 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:51.180 [info] create table custom_pk
10:41:51.180 [info] create table no_pk
10:41:51.181 [info] create table composite_pk
10:41:51.181 [info] == Migrated 20240101000006 in 0.0s
10:41:51.181 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:51.181 [info] create table timestamps
10:41:51.181 [info] == Migrated 20240101000007 in 0.0s
10:41:51.182 [info] Cleared entire schema cache
10:41:51.254 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:51.254 [info] create table users
10:41:51.254 [info] == Migrated 20240101000001 in 0.0s
10:41:51.255 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:51.255 [info] create table groups
10:41:51.255 [info] == Migrated 20240101000002 in 0.0s
10:41:51.255 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:51.255 [info] create table user_groups
10:41:51.256 [info] create index user_groups_user_id_group_id_index
10:41:51.256 [info] == Migrated 20240101000003 in 0.0s
10:41:51.256 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:51.256 [info] create table association_parents
10:41:51.256 [info] create table associations
10:41:51.256 [info] create table association_items
10:41:51.256 [info] == Migrated 20240101000004 in 0.0s
10:41:51.256 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:51.256 [info] create table basic_types
10:41:51.257 [info] == Migrated 20240101000005 in 0.0s
10:41:51.257 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:51.257 [info] create table custom_pk
10:41:51.257 [info] create table no_pk
10:41:51.257 [info] create table composite_pk
10:41:51.257 [info] == Migrated 20240101000006 in 0.0s
10:41:51.258 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:51.258 [info] create table timestamps
10:41:51.258 [info] == Migrated 20240101000007 in 0.0s
10:41:51.259 [info] Cleared entire schema cache
10:41:51.332 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:51.332 [info] create table users
10:41:51.332 [info] == Migrated 20240101000001 in 0.0s
10:41:51.333 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:51.333 [info] create table groups
10:41:51.333 [info] == Migrated 20240101000002 in 0.0s
10:41:51.333 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:51.333 [info] create table user_groups
10:41:51.333 [info] create index user_groups_user_id_group_id_index
10:41:51.333 [info] == Migrated 20240101000003 in 0.0s
10:41:51.333 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:51.334 [info] create table association_parents
10:41:51.334 [info] create table associations
10:41:51.334 [info] create table association_items
10:41:51.334 [info] == Migrated 20240101000004 in 0.0s
10:41:51.334 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:51.334 [info] create table basic_types
10:41:51.334 [info] == Migrated 20240101000005 in 0.0s
10:41:51.334 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:51.334 [info] create table custom_pk
10:41:51.335 [info] create table no_pk
10:41:51.335 [info] create table composite_pk
10:41:51.335 [info] == Migrated 20240101000006 in 0.0s
10:41:51.335 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:51.335 [info] create table timestamps
10:41:51.335 [info] == Migrated 20240101000007 in 0.0s
10:41:51.336 [info] Cleared entire schema cache
10:41:51.408 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:51.409 [info] create table users
10:41:51.409 [info] == Migrated 20240101000001 in 0.0s
10:41:51.409 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:51.409 [info] create table groups
10:41:51.409 [info] == Migrated 20240101000002 in 0.0s
10:41:51.409 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:51.409 [info] create table user_groups
10:41:51.409 [info] create index user_groups_user_id_group_id_index
10:41:51.410 [info] == Migrated 20240101000003 in 0.0s
10:41:51.410 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:51.410 [info] create table association_parents
10:41:51.410 [info] create table associations
10:41:51.410 [info] create table association_items
10:41:51.410 [info] == Migrated 20240101000004 in 0.0s
10:41:51.410 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:51.410 [info] create table basic_types
10:41:51.411 [info] == Migrated 20240101000005 in 0.0s
10:41:51.411 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:51.411 [info] create table custom_pk
10:41:51.411 [info] create table no_pk
10:41:51.411 [info] create table composite_pk
10:41:51.411 [info] == Migrated 20240101000006 in 0.0s
10:41:51.411 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:51.411 [info] create table timestamps
10:41:51.411 [info] == Migrated 20240101000007 in 0.0s
10:41:51.412 [info] Cleared entire schema cache
10:41:51.486 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
10:41:51.486 [info] create table users
10:41:51.486 [info] == Migrated 20240101000001 in 0.0s
10:41:51.486 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
10:41:51.486 [info] create table groups
10:41:51.486 [info] == Migrated 20240101000002 in 0.0s
10:41:51.487 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
10:41:51.487 [info] create table user_groups
10:41:51.487 [info] create index user_groups_user_id_group_id_index
10:41:51.487 [info] == Migrated 20240101000003 in 0.0s
10:41:51.487 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
10:41:51.487 [info] create table association_parents
10:41:51.487 [info] create table associations
10:41:51.487 [info] create table association_items
10:41:51.487 [info] == Migrated 20240101000004 in 0.0s
10:41:51.487 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
10:41:51.488 [info] create table basic_types
10:41:51.488 [info] == Migrated 20240101000005 in 0.0s
10:41:51.488 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
10:41:51.488 [info] create table custom_pk
10:41:51.488 [info] create table no_pk
10:41:51.488 [info] create table composite_pk
10:41:51.488 [info] == Migrated 20240101000006 in 0.0s
10:41:51.488 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
10:41:51.488 [info] create table timestamps
10:41:51.488 [info] == Migrated 20240101000007 in 0.0s
10:41:51.489 [info] Cleared entire schema cache
11:08:57.690 [info] Drops.Relation.SchemaCache started with persistent storage at tmp/cache/test/drops_schema/drops_schema_cache.dets
11:09:50.926 [info] Drops.Relation.SchemaCache started with persistent storage at tmp/cache/test/drops_schema/drops_schema_cache.dets
11:13:03.394 [info] Drops.Relation.SchemaCache started with persistent storage at tmp/cache/test/drops_schema/drops_schema_cache.dets
11:13:07.257 [info] Drops.Relation.SchemaCache started with persistent storage at tmp/cache/test/drops_schema/drops_schema_cache.dets
11:13:29.340 [info] Drops.Relation.SchemaCache started with persistent storage at tmp/cache/test/drops_schema/drops_schema_cache.dets
11:14:00.487 [info] Drops.Relation.SchemaCache started with persistent storage at tmp/cache/test/drops_schema/drops_schema_cache.dets
11:14:14.330 [info] Drops.Relation.SchemaCache started with persistent storage at tmp/cache/test/drops_schema/drops_schema_cache.dets
11:14:14.848 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
11:14:14.848 [info] create table users
11:14:14.849 [info] == Migrated 20240101000001 in 0.0s
11:14:14.857 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
11:14:14.857 [info] create table groups
11:14:14.857 [info] == Migrated 20240101000002 in 0.0s
11:14:14.857 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
11:14:14.857 [info] create table user_groups
11:14:14.857 [info] create index user_groups_user_id_group_id_index
11:14:14.857 [info] == Migrated 20240101000003 in 0.0s
11:14:14.858 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
11:14:14.858 [info] create table association_parents
11:14:14.858 [info] create table associations
11:14:14.858 [info] create table association_items
11:14:14.858 [info] == Migrated 20240101000004 in 0.0s
11:14:14.858 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
11:14:14.858 [info] create table basic_types
11:14:14.861 [info] == Migrated 20240101000005 in 0.0s
11:14:14.862 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
11:14:14.862 [info] create table custom_pk
11:14:14.862 [info] create table no_pk
11:14:14.862 [info] create table composite_pk
11:14:14.862 [info] == Migrated 20240101000006 in 0.0s
11:14:14.862 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
11:14:14.862 [info] create table timestamps
11:14:14.862 [info] == Migrated 20240101000007 in 0.0s
11:14:14.892 [info] Cleared entire schema cache
11:14:14.994 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
11:14:14.994 [info] create table users
11:14:14.994 [info] == Migrated 20240101000001 in 0.0s
11:14:14.994 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
11:14:14.994 [info] create table groups
11:14:14.994 [info] == Migrated 20240101000002 in 0.0s
11:14:14.995 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
11:14:14.995 [info] create table user_groups
11:14:14.995 [info] create index user_groups_user_id_group_id_index
11:14:14.995 [info] == Migrated 20240101000003 in 0.0s
11:14:14.995 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
11:14:14.995 [info] create table association_parents
11:14:14.995 [info] create table associations
11:14:14.995 [info] create table association_items
11:14:14.995 [info] == Migrated 20240101000004 in 0.0s
11:14:14.995 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
11:14:14.995 [info] create table basic_types
11:14:14.996 [info] == Migrated 20240101000005 in 0.0s
11:14:14.996 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
11:14:14.996 [info] create table custom_pk
11:14:14.996 [info] create table no_pk
11:14:14.996 [info] create table composite_pk
11:14:14.996 [info] == Migrated 20240101000006 in 0.0s
11:14:14.996 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
11:14:14.996 [info] create table timestamps
11:14:14.996 [info] == Migrated 20240101000007 in 0.0s
11:14:14.998 [info] Cleared entire schema cache
11:14:15.072 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
11:14:15.072 [info] create table users
11:14:15.072 [info] == Migrated 20240101000001 in 0.0s
11:14:15.072 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
11:14:15.073 [info] create table groups
11:14:15.073 [info] == Migrated 20240101000002 in 0.0s
11:14:15.073 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
11:14:15.073 [info] create table user_groups
11:14:15.073 [info] create index user_groups_user_id_group_id_index
11:14:15.073 [info] == Migrated 20240101000003 in 0.0s
11:14:15.073 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
11:14:15.073 [info] create table association_parents
11:14:15.074 [info] create table associations
11:14:15.074 [info] create table association_items
11:14:15.074 [info] == Migrated 20240101000004 in 0.0s
11:14:15.074 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
11:14:15.074 [info] create table basic_types
11:14:15.074 [info] == Migrated 20240101000005 in 0.0s
11:14:15.074 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
11:14:15.074 [info] create table custom_pk
11:14:15.074 [info] create table no_pk
11:14:15.074 [info] create table composite_pk
11:14:15.074 [info] == Migrated 20240101000006 in 0.0s
11:14:15.075 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
11:14:15.075 [info] create table timestamps
11:14:15.075 [info] == Migrated 20240101000007 in 0.0s
11:14:15.077 [info] Cleared entire schema cache
11:14:15.148 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
11:14:15.148 [info] create table users
11:14:15.149 [info] == Migrated 20240101000001 in 0.0s
11:14:15.149 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
11:14:15.149 [info] create table groups
11:14:15.149 [info] == Migrated 20240101000002 in 0.0s
11:14:15.149 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
11:14:15.149 [info] create table user_groups
11:14:15.149 [info] create index user_groups_user_id_group_id_index
11:14:15.149 [info] == Migrated 20240101000003 in 0.0s
11:14:15.150 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
11:14:15.150 [info] create table association_parents
11:14:15.150 [info] create table associations
11:14:15.150 [info] create table association_items
11:14:15.150 [info] == Migrated 20240101000004 in 0.0s
11:14:15.150 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
11:14:15.150 [info] create table basic_types
11:14:15.150 [info] == Migrated 20240101000005 in 0.0s
11:14:15.151 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
11:14:15.151 [info] create table custom_pk
11:14:15.151 [info] create table no_pk
11:14:15.151 [info] create table composite_pk
11:14:15.151 [info] == Migrated 20240101000006 in 0.0s
11:14:15.151 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
11:14:15.151 [info] create table timestamps
11:14:15.151 [info] == Migrated 20240101000007 in 0.0s
11:14:15.153 [info] Cleared entire schema cache
11:14:15.227 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
11:14:15.227 [info] create table users
11:14:15.227 [info] == Migrated 20240101000001 in 0.0s
11:14:15.227 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
11:14:15.228 [info] create table groups
11:14:15.228 [info] == Migrated 20240101000002 in 0.0s
11:14:15.228 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
11:14:15.228 [info] create table user_groups
11:14:15.228 [info] create index user_groups_user_id_group_id_index
11:14:15.228 [info] == Migrated 20240101000003 in 0.0s
11:14:15.228 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
11:14:15.229 [info] create table association_parents
11:14:15.229 [info] create table associations
11:14:15.229 [info] create table association_items
11:14:15.229 [info] == Migrated 20240101000004 in 0.0s
11:14:15.229 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
11:14:15.229 [info] create table basic_types
11:14:15.229 [info] == Migrated 20240101000005 in 0.0s
11:14:15.229 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
11:14:15.229 [info] create table custom_pk
11:14:15.230 [info] create table no_pk
11:14:15.230 [info] create table composite_pk
11:14:15.230 [info] == Migrated 20240101000006 in 0.0s
11:14:15.230 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
11:14:15.230 [info] create table timestamps
11:14:15.230 [info] == Migrated 20240101000007 in 0.0s
11:14:15.232 [info] Cleared entire schema cache
11:14:15.304 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
11:14:15.304 [info] create table users
11:14:15.304 [info] == Migrated 20240101000001 in 0.0s
11:14:15.304 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
11:14:15.304 [info] create table groups
11:14:15.304 [info] == Migrated 20240101000002 in 0.0s
11:14:15.305 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
11:14:15.305 [info] create table user_groups
11:14:15.305 [info] create index user_groups_user_id_group_id_index
11:14:15.305 [info] == Migrated 20240101000003 in 0.0s
11:14:15.305 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
11:14:15.305 [info] create table association_parents
11:14:15.305 [info] create table associations
11:14:15.305 [info] create table association_items
11:14:15.305 [info] == Migrated 20240101000004 in 0.0s
11:14:15.306 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
11:14:15.306 [info] create table basic_types
11:14:15.306 [info] == Migrated 20240101000005 in 0.0s
11:14:15.306 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
11:14:15.306 [info] create table custom_pk
11:14:15.306 [info] create table no_pk
11:14:15.306 [info] create table composite_pk
11:14:15.306 [info] == Migrated 20240101000006 in 0.0s
11:14:15.306 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
11:14:15.306 [info] create table timestamps
11:14:15.307 [info] == Migrated 20240101000007 in 0.0s
11:14:15.308 [info] Cleared entire schema cache
11:14:15.382 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
11:14:15.382 [info] create table users
11:14:15.382 [info] == Migrated 20240101000001 in 0.0s
11:14:15.382 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
11:14:15.382 [info] create table groups
11:14:15.382 [info] == Migrated 20240101000002 in 0.0s
11:14:15.383 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
11:14:15.383 [info] create table user_groups
11:14:15.383 [info] create index user_groups_user_id_group_id_index
11:14:15.383 [info] == Migrated 20240101000003 in 0.0s
11:14:15.383 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
11:14:15.383 [info] create table association_parents
11:14:15.383 [info] create table associations
11:14:15.383 [info] create table association_items
11:14:15.383 [info] == Migrated 20240101000004 in 0.0s
11:14:15.384 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
11:14:15.384 [info] create table basic_types
11:14:15.384 [info] == Migrated 20240101000005 in 0.0s
11:14:15.384 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
11:14:15.384 [info] create table custom_pk
11:14:15.384 [info] create table no_pk
11:14:15.384 [info] create table composite_pk
11:14:15.384 [info] == Migrated 20240101000006 in 0.0s
11:14:15.384 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
11:14:15.384 [info] create table timestamps
11:14:15.385 [info] == Migrated 20240101000007 in 0.0s
11:14:15.386 [info] Cleared entire schema cache
11:14:15.458 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
11:14:15.458 [info] create table users
11:14:15.459 [info] == Migrated 20240101000001 in 0.0s
11:14:15.459 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
11:14:15.459 [info] create table groups
11:14:15.459 [info] == Migrated 20240101000002 in 0.0s
11:14:15.459 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
11:14:15.459 [info] create table user_groups
11:14:15.459 [info] create index user_groups_user_id_group_id_index
11:14:15.460 [info] == Migrated 20240101000003 in 0.0s
11:14:15.460 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
11:14:15.460 [info] create table association_parents
11:14:15.460 [info] create table associations
11:14:15.460 [info] create table association_items
11:14:15.460 [info] == Migrated 20240101000004 in 0.0s
11:14:15.460 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
11:14:15.460 [info] create table basic_types
11:14:15.461 [info] == Migrated 20240101000005 in 0.0s
11:14:15.461 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
11:14:15.461 [info] create table custom_pk
11:14:15.461 [info] create table no_pk
11:14:15.461 [info] create table composite_pk
11:14:15.461 [info] == Migrated 20240101000006 in 0.0s
11:14:15.461 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
11:14:15.461 [info] create table timestamps
11:14:15.461 [info] == Migrated 20240101000007 in 0.0s
11:14:15.463 [info] Cleared entire schema cache
11:14:15.536 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
11:14:15.536 [info] create table users
11:14:15.536 [info] == Migrated 20240101000001 in 0.0s
11:14:15.536 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
11:14:15.536 [info] create table groups
11:14:15.536 [info] == Migrated 20240101000002 in 0.0s
11:14:15.537 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
11:14:15.537 [info] create table user_groups
11:14:15.537 [info] create index user_groups_user_id_group_id_index
11:14:15.537 [info] == Migrated 20240101000003 in 0.0s
11:14:15.537 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
11:14:15.537 [info] create table association_parents
11:14:15.537 [info] create table associations
11:14:15.537 [info] create table association_items
11:14:15.537 [info] == Migrated 20240101000004 in 0.0s
11:14:15.538 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
11:14:15.538 [info] create table basic_types
11:14:15.538 [info] == Migrated 20240101000005 in 0.0s
11:14:15.538 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
11:14:15.538 [info] create table custom_pk
11:14:15.538 [info] create table no_pk
11:14:15.538 [info] create table composite_pk
11:14:15.538 [info] == Migrated 20240101000006 in 0.0s
11:14:15.538 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
11:14:15.538 [info] create table timestamps
11:14:15.539 [info] == Migrated 20240101000007 in 0.0s
11:14:15.559 [info] Cleared entire schema cache
11:14:15.612 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
11:14:15.612 [info] create table users
11:14:15.612 [info] == Migrated 20240101000001 in 0.0s
11:14:15.613 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
11:14:15.613 [info] create table groups
11:14:15.613 [info] == Migrated 20240101000002 in 0.0s
11:14:15.613 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
11:14:15.613 [info] create table user_groups
11:14:15.613 [info] create index user_groups_user_id_group_id_index
11:14:15.613 [info] == Migrated 20240101000003 in 0.0s
11:14:15.613 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
11:14:15.613 [info] create table association_parents
11:14:15.614 [info] create table associations
11:14:15.614 [info] create table association_items
11:14:15.614 [info] == Migrated 20240101000004 in 0.0s
11:14:15.614 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
11:14:15.614 [info] create table basic_types
11:14:15.614 [info] == Migrated 20240101000005 in 0.0s
11:14:15.614 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
11:14:15.614 [info] create table custom_pk
11:14:15.614 [info] create table no_pk
11:14:15.614 [info] create table composite_pk
11:14:15.614 [info] == Migrated 20240101000006 in 0.0s
11:14:15.615 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
11:14:15.615 [info] create table timestamps
11:14:15.615 [info] == Migrated 20240101000007 in 0.0s
11:14:15.617 [info] Cleared entire schema cache
11:14:15.689 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
11:14:15.689 [info] create table users
11:14:15.689 [info] == Migrated 20240101000001 in 0.0s
11:14:15.689 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
11:14:15.689 [info] create table groups
11:14:15.689 [info] == Migrated 20240101000002 in 0.0s
11:14:15.690 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
11:14:15.690 [info] create table user_groups
11:14:15.690 [info] create index user_groups_user_id_group_id_index
11:14:15.690 [info] == Migrated 20240101000003 in 0.0s
11:14:15.690 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
11:14:15.690 [info] create table association_parents
11:14:15.690 [info] create table associations
11:14:15.690 [info] create table association_items
11:14:15.690 [info] == Migrated 20240101000004 in 0.0s
11:14:15.691 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
11:14:15.691 [info] create table basic_types
11:14:15.691 [info] == Migrated 20240101000005 in 0.0s
11:14:15.691 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
11:14:15.691 [info] create table custom_pk
11:14:15.691 [info] create table no_pk
11:14:15.691 [info] create table composite_pk
11:14:15.691 [info] == Migrated 20240101000006 in 0.0s
11:14:15.691 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
11:14:15.691 [info] create table timestamps
11:14:15.691 [info] == Migrated 20240101000007 in 0.0s
11:14:15.693 [info] Cleared entire schema cache
11:14:15.765 [info] == Running 20240101000001 Drops.TestRepo.Migrations.CreateUsersTable.change/0 forward
11:14:15.765 [info] create table users
11:14:15.765 [info] == Migrated 20240101000001 in 0.0s
11:14:15.766 [info] == Running 20240101000002 Drops.TestRepo.Migrations.CreateGroupsTable.change/0 forward
11:14:15.766 [info] create table groups
11:14:15.766 [info] == Migrated 20240101000002 in 0.0s
11:14:15.766 [info] == Running 20240101000003 Drops.TestRepo.Migrations.CreateUserGroupsTable.change/0 forward
11:14:15.766 [info] create table user_groups
11:14:15.766 [info] create index user_groups_user_id_group_id_index
11:14:15.766 [info] == Migrated 20240101000003 in 0.0s
11:14:15.767 [info] == Running 20240101000004 Drops.TestRepo.Migrations.CreateAssociationsTables.change/0 forward
11:14:15.767 [info] create table association_parents
11:14:15.767 [info] create table associations
11:14:15.767 [info] create table association_items
11:14:15.767 [info] == Migrated 20240101000004 in 0.0s
11:14:15.767 [info] == Running 20240101000005 Drops.TestRepo.Migrations.CreateBasicTypesTable.change/0 forward
11:14:15.767 [info] create table basic_types
11:14:15.767 [info] == Migrated 20240101000005 in 0.0s
11:14:15.767 [info] == Running 20240101000006 Drops.TestRepo.Migrations.CreateSpecialPkTables.change/0 forward
11:14:15.767 [info] create table custom_pk
11:14:15.768 [info] create table no_pk
11:14:15.768 [info] create table composite_pk
11:14:15.768 [info] == Migrated 20240101000006 in 0.0s
11:14:15.768 [info] == Running 20240101000007 Drops.TestRepo.Migrations.CreateTimestampsTable.change/0 forward
11:14:15.768 [info] create table timestamps
11:14:15.768 [info] == Migrated 20240101000007 in 0.0s
11:14:15.770 [info] Cleared entire schema cache
