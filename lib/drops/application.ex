defmodule Drops.Application do
  @moduledoc false

  use Application

  alias Drops.Config

  @impl true
  def start(_type, _opts) do
    config = Config.validate!()
    :ok = Config.persist(config)

    register_builtin_types()

    children =
      [
        # Start schema cache if enabled
        {Drops.Relation.SchemaCache, []}
      ] ++ ecto_children()

    # Filter out children that shouldn't start based on configuration
    children = Enum.filter(children, &should_start_child?/1)

    opts = [strategy: :one_for_one, name: Drops.Supervisor]
    Supervisor.start_link(children, opts)
  end

  defp ecto_children do
    if Mix.env() in [:dev, :test] and Code.ensure_loaded?(Drops.TestSupport) do
      # Ensure test repo is loaded
      Drops.TestSupport.ensure_test_repo_loaded()

      # Try to start the TestRepo if it's available
      case Code.ensure_compiled(Drops.TestRepo) do
        {:module, _} -> [Drops.TestRepo]
        _ -> []
      end
    else
      []
    end
  end

  defp should_start_child?({Drops.Relation.SchemaCache, _opts}) do
    Config.schema_cache()[:enabled]
  end

  defp should_start_child?(_child), do: true

  defp register_builtin_types do
    builtin_types = [
      Drops.Types.Cast,
      Drops.Types.List,
      Drops.Types.Map,
      Drops.Types.Number,
      Drops.Types.Primitive,
      Drops.Types.Union
    ]

    Enum.each(builtin_types, &Drops.Type.register_type/1)
  end
end
